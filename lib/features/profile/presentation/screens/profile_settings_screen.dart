import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:go_router/go_router.dart';
import 'package:nextsportz_v2/core/config/theme.dart';
import '../../../../utils/color.dart';
import '../../../../core/widgets/cached_image_widget.dart';
import '../../profile_providers.dart';
import '../../domain/entities/profile.dart';
import '../../../../core/providers/theme_provider.dart' as theme_providers;
import '../widgets/profile_shimmer.dart';

class ProfileSettingsScreen extends ConsumerStatefulWidget {
  const ProfileSettingsScreen({Key? key}) : super(key: key);

  @override
  ConsumerState<ProfileSettingsScreen> createState() =>
      _ProfileSettingsScreenState();
}

class _ProfileSettingsScreenState extends ConsumerState<ProfileSettingsScreen> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _nameController;
  late TextEditingController _emailController;
  late TextEditingController _phoneController;
  final _imagePicker = ImagePicker();
  DateTime? _selectedDateOfBirth;
  File? _selectedImage;
  Uint8List? _selectedImageBytes;
  bool _isUploadingImage = false;

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController();
    _emailController = TextEditingController();
    _phoneController = TextEditingController();

    // Load profile data when screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(profileProvider.notifier).loadProfile();
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Update form data when profile changes
    final profile = ref.read(profileProvider).value;
    print('didChangeDependencies - Profile: ${profile?.toJson()}');
    print(
      'didChangeDependencies - Current _selectedDateOfBirth: $_selectedDateOfBirth',
    );

    if (profile != null) {
      _nameController.text = profile.name;
      _emailController.text = profile.email;
      _phoneController.text = profile.phoneNumber;
      // Only update DOB if it's null or if the profile has a different DOB
      if (_selectedDateOfBirth == null ||
          _selectedDateOfBirth != profile.dateOfBirth) {
        print(
          'Updating _selectedDateOfBirth from ${_selectedDateOfBirth} to ${profile.dateOfBirth}',
        );
        setState(() {
          _selectedDateOfBirth = profile.dateOfBirth;
        });
      } else {
        print('No DOB update needed');
      }
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final profileAsync = ref.watch(profileProvider);

    return Scaffold(
      backgroundColor: PrimeryColor,
      appBar: AppBar(
        backgroundColor: PrimeryColor,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => context.go('/home'),
        ),
        title: const Text(
          'Profile Settings',
          style: TextStyle(
            fontFamily: 'Gilroy_Bold',
            color: Colors.white,
            fontSize: 18,
          ),
        ),
        actions: [
          Consumer(
            builder: (context, ref, child) {
              final currentProfile = ref.watch(profileProvider).value;
              final hasChanges =
                  currentProfile?.dateOfBirth != _selectedDateOfBirth;

              return TextButton(
                onPressed: hasChanges ? _saveProfile : null,
                child: Text(
                  'Save',
                  style: TextStyle(
                    fontFamily: 'Gilroy_Medium',
                    color:
                        hasChanges
                            ? NextSportzTheme.getAccentColor(
                              ref.watch(theme_providers.isDarkModeProvider),
                            )
                            : Colors.grey,
                    fontSize: 16,
                  ),
                ),
              );
            },
          ),
        ],
      ),
      body: profileAsync.when(
        data: (profile) => _buildForm(profile),
        loading: () => const ProfileShimmer(),
        error:
            (error, stack) => Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.error_outline, color: Colors.red, size: 64),
                  const SizedBox(height: 16),
                  Text(
                    'Error loading profile',
                    style: TextStyle(
                      fontFamily: 'Gilroy_Medium',
                      color: Colors.white,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      ref.read(profileProvider.notifier).loadProfile();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: NextSportzTheme.getAccentColor(
                        ref.watch(theme_providers.isDarkModeProvider),
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: const Text(
                      'Retry',
                      style: TextStyle(
                        fontFamily: 'Gilroy_Medium',
                        color: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
            ),
      ),
    );
  }

  Widget _buildForm(Profile? profile) {
    // Note: Profile data population is now handled in didChangeDependencies
    // to avoid resetting user selections during rebuilds

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 20),

            // Profile Image Section
            Center(
              child: Column(
                children: [
                  Stack(
                    children: [
                      Container(
                        width: 120,
                        height: 120,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: NextSportzTheme.getAccentColor(
                              ref.watch(theme_providers.isDarkModeProvider),
                            ),
                            width: 3,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: NextSportzTheme.getAccentColor(
                                ref.watch(theme_providers.isDarkModeProvider),
                              ).withOpacity(0.3),
                              blurRadius: 15,
                              offset: const Offset(0, 8),
                            ),
                          ],
                        ),
                        child: ClipOval(child: _buildProfileImage(profile)),
                      ),
                      Positioned(
                        bottom: 0,
                        right: 0,
                        child: GestureDetector(
                          onTap: _pickImage,
                          child: Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: NextSportzTheme.getAccentColor(
                                ref.watch(theme_providers.isDarkModeProvider),
                              ),
                              shape: BoxShape.circle,
                              boxShadow: [
                                BoxShadow(
                                  color: NextSportzTheme.getAccentColor(
                                    ref.watch(
                                      theme_providers.isDarkModeProvider,
                                    ),
                                  ).withOpacity(0.3),
                                  blurRadius: 8,
                                  offset: const Offset(0, 4),
                                ),
                              ],
                            ),
                            child: const Icon(
                              Icons.camera_alt,
                              color: Colors.white,
                              size: 20,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  _isUploadingImage
                      ? CircularProgressIndicator(
                        color: NextSportzTheme.getAccentColor(
                          ref.watch(theme_providers.isDarkModeProvider),
                        ),
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          NextSportzTheme.getAccentColor(
                            ref.watch(theme_providers.isDarkModeProvider),
                          ),
                        ),
                      )
                      : TextButton(
                        onPressed: _pickImage,
                        child: Text(
                          'Change Photo',
                          style: TextStyle(
                            fontFamily: 'Gilroy_Medium',
                            color: NextSportzTheme.getAccentColor(
                              ref.watch(theme_providers.isDarkModeProvider),
                            ),
                            fontSize: 16,
                          ),
                        ),
                      ),
                ],
              ),
            ),

            const SizedBox(height: 32),

            // Form Fields
            _buildReadOnlyTextField(
              controller: _nameController,
              label: 'Full Name',
              icon: Icons.person,
              subtitle: 'Contact support to update your name',
            ),

            const SizedBox(height: 20),

            _buildReadOnlyTextField(
              controller: _emailController,
              label: 'Email',
              icon: Icons.email,
              subtitle: 'Contact support to update your email',
            ),

            const SizedBox(height: 20),

            _buildReadOnlyTextField(
              controller: _phoneController,
              label: 'Phone Number',
              icon: Icons.phone,
              subtitle: 'Contact support to update your phone number',
            ),

            const SizedBox(height: 20),

            // Date of Birth Field
            _buildDateOfBirthField(),

            const SizedBox(height: 20),

            // Information Section
            _buildInformationSection(),

            const SizedBox(height: 20),

            // Additional Settings
            _buildSettingsSection(),

            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
  }) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(15),
        color: lightblue.withOpacity(0.5),
        border: Border.all(color: Colors.white.withOpacity(0.1), width: 1),
      ),
      child: TextFormField(
        controller: controller,
        keyboardType: keyboardType,
        validator: validator,
        style: const TextStyle(
          fontFamily: 'Gilroy_Medium',
          color: Colors.white,
          fontSize: 16,
        ),
        decoration: InputDecoration(
          labelText: label,
          labelStyle: TextStyle(
            fontFamily: 'Gilroy_Medium',
            color: grey,
            fontSize: 14,
          ),
          prefixIcon: Icon(
            icon,
            color: NextSportzTheme.getAccentColor(
              ref.watch(theme_providers.isDarkModeProvider),
            ),
            size: 20,
          ),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 20,
            vertical: 16,
          ),
        ),
      ),
    );
  }

  Widget _buildReadOnlyTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    required String subtitle,
  }) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(15),
        color: Colors.grey.withOpacity(0.2),
        border: Border.all(color: Colors.white.withOpacity(0.1), width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
            child: Row(
              children: [
                Icon(icon, color: Colors.grey, size: 20),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        label,
                        style: TextStyle(
                          fontFamily: 'Gilroy_Medium',
                          color: grey,
                          fontSize: 14,
                        ),
                      ),
                      Text(
                        controller.text.isEmpty ? 'Not set' : controller.text,
                        style: const TextStyle(
                          fontFamily: 'Gilroy_Medium',
                          color: Colors.white,
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                ),
                Icon(Icons.lock, color: Colors.grey, size: 16),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.fromLTRB(20, 0, 20, 12),
            child: Text(
              subtitle,
              style: TextStyle(
                fontFamily: 'Gilroy_Medium',
                color: Colors.grey.withOpacity(0.7),
                fontSize: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDateOfBirthField() {
    print(
      'Building DOB field with _selectedDateOfBirth: $_selectedDateOfBirth',
    );

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(15),
        color: lightblue.withOpacity(0.5),
        border: Border.all(color: Colors.white.withOpacity(0.1), width: 1),
      ),
      child: InkWell(
        onTap: () => _selectDateOfBirth(),
        borderRadius: BorderRadius.circular(15),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
          child: Row(
            children: [
              Icon(
                Icons.calendar_today,
                color: NextSportzTheme.getAccentColor(
                  ref.watch(theme_providers.isDarkModeProvider),
                ),
                size: 20,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Date of Birth',
                      style: TextStyle(
                        fontFamily: 'Gilroy_Medium',
                        color: grey,
                        fontSize: 14,
                      ),
                    ),
                    Text(
                      _selectedDateOfBirth != null
                          ? '${_selectedDateOfBirth!.day}/${_selectedDateOfBirth!.month}/${_selectedDateOfBirth!.year}'
                          : 'Select your date of birth',
                      style: TextStyle(
                        fontFamily: 'Gilroy_Medium',
                        color:
                            _selectedDateOfBirth != null ? Colors.white : grey,
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
              ),
              Icon(Icons.arrow_forward_ios, color: Colors.grey, size: 16),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _selectDateOfBirth() async {
    print('Current _selectedDateOfBirth: $_selectedDateOfBirth');

    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate:
          _selectedDateOfBirth ??
          DateTime.now().subtract(const Duration(days: 6570)), // 18 years ago
      firstDate: DateTime.now().subtract(
        const Duration(days: 36500),
      ), // 100 years ago
      lastDate: DateTime.now().subtract(
        const Duration(days: 6570),
      ), // 18 years ago
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.dark(
              primary: NextSportzTheme.getAccentColor(
                ref.watch(theme_providers.isDarkModeProvider),
              ),
              onPrimary: Colors.white,
              surface: Color(0xFF1A1A1A),
              onSurface: Colors.white,
            ),
          ),
          child: child!,
        );
      },
    );

    print('Picked date: $picked');
    print('Current _selectedDateOfBirth before update: $_selectedDateOfBirth');

    if (picked != null && picked != _selectedDateOfBirth) {
      setState(() {
        _selectedDateOfBirth = picked;
      });
      print('Updated _selectedDateOfBirth to: $_selectedDateOfBirth');
    } else {
      print(
        'No update needed - picked: $picked, current: $_selectedDateOfBirth',
      );
    }
  }

  Widget _buildInformationSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        color: Colors.orange.withOpacity(0.1),
        border: Border.all(color: Colors.orange.withOpacity(0.3), width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.info_outline, color: Colors.orange, size: 20),
              const SizedBox(width: 8),
              Text(
                'Profile Update Information',
                style: TextStyle(
                  fontFamily: 'Gilroy_Bold',
                  color: Colors.orange,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            'Only your Date of Birth can be updated through this form. To update your name, email, or phone number, please contact our support team.',
            style: TextStyle(
              fontFamily: 'Gilroy_Medium',
              color: Colors.white.withOpacity(0.8),
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'This ensures the security and integrity of your account information.',
            style: TextStyle(
              fontFamily: 'Gilroy_Medium',
              color: Colors.white.withOpacity(0.6),
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        color: lightblue.withOpacity(0.5),
        border: Border.all(color: Colors.white.withOpacity(0.1), width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Settings',
            style: TextStyle(
              fontFamily: 'Gilroy_Bold',
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          _buildSettingItem(
            'Notifications',
            'Receive push notifications',
            Icons.notifications,
            true,
            (value) {
              // TODO: Update notification settings
            },
          ),
          const SizedBox(height: 12),
          _buildThemeSettingItem(),
          const SizedBox(height: 12),
          _buildSettingItem(
            'Location Services',
            'Allow location access',
            Icons.location_on,
            true,
            (value) {
              // TODO: Update location settings
            },
          ),
        ],
      ),
    );
  }

  Widget _buildThemeSettingItem() {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final themeNotifier = ref.read(theme_providers.themeProvider.notifier);

    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: NextSportzTheme.getAccentColor(
              ref.watch(theme_providers.isDarkModeProvider),
            ).withOpacity(0.2),
            borderRadius: BorderRadius.circular(10),
          ),
          child: Icon(
            isDark ? Icons.dark_mode : Icons.light_mode,
            color: NextSportzTheme.getAccentColor(
              ref.watch(theme_providers.isDarkModeProvider),
            ),
            size: 20,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                isDark ? 'Dark Mode' : 'Light Mode',
                style: const TextStyle(
                  fontFamily: 'Gilroy_Medium',
                  color: Colors.white,
                  fontSize: 16,
                ),
              ),
              Text(
                isDark ? 'Dark theme enabled' : 'Light theme enabled',
                style: TextStyle(
                  fontFamily: 'Gilroy_Medium',
                  color: grey,
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ),
        Switch(
          value: isDark,
          onChanged: (_) {
            print('Switch toggled - current isDark: $isDark');
            themeNotifier.toggleTheme();
          },
          activeColor: NextSportzTheme.getAccentColor(
            ref.watch(theme_providers.isDarkModeProvider),
          ),
          activeTrackColor: NextSportzTheme.getAccentColor(
            ref.watch(theme_providers.isDarkModeProvider),
          ).withOpacity(0.3),
        ),
      ],
    );
  }

  Widget _buildSettingItem(
    String title,
    String subtitle,
    IconData icon,
    bool initialValue,
    Function(bool) onChanged,
  ) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: NextSportzTheme.getAccentColor(
              ref.watch(theme_providers.isDarkModeProvider),
            ).withOpacity(0.2),
            borderRadius: BorderRadius.circular(10),
          ),
          child: Icon(
            icon,
            color: NextSportzTheme.getAccentColor(
              ref.watch(theme_providers.isDarkModeProvider),
            ),
            size: 20,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontFamily: 'Gilroy_Medium',
                  color: Colors.white,
                  fontSize: 16,
                ),
              ),
              Text(
                subtitle,
                style: TextStyle(
                  fontFamily: 'Gilroy_Medium',
                  color: grey,
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ),
        Switch(
          value: initialValue,
          onChanged: onChanged,
          activeColor: NextSportzTheme.getAccentColor(
            ref.watch(theme_providers.isDarkModeProvider),
          ),
          activeTrackColor: NextSportzTheme.getAccentColor(
            ref.watch(theme_providers.isDarkModeProvider),
          ).withOpacity(0.3),
        ),
      ],
    );
  }

  Widget _buildProfileImage(Profile? profile) {
    // Show selected image if available
    if (_selectedImage != null || _selectedImageBytes != null) {
      if (kIsWeb && _selectedImageBytes != null) {
        return Image.memory(
          _selectedImageBytes!,
          width: 120,
          height: 120,
          fit: BoxFit.cover,
        );
      } else if (_selectedImage != null) {
        return Image.file(
          _selectedImage!,
          width: 120,
          height: 120,
          fit: BoxFit.cover,
        );
      }
    }

    // Show current profile image or fallback
    if (profile?.photoUrl != null && profile!.photoUrl!.isNotEmpty) {
      return CachedImageWidget(
        imageUrl: profile.photoUrl!,
        width: 120,
        height: 120,
        fit: BoxFit.cover,
        fallbackIcon: Icons.person,
        errorWidget: _buildFallbackAvatar(),
      );
    }

    return _buildFallbackAvatar();
  }

  Widget _buildFallbackAvatar() {
    return Container(
      width: 120,
      height: 120,
      decoration: BoxDecoration(shape: BoxShape.circle, color: lightblue),
      child: const Icon(Icons.person, size: 60, color: Colors.white),
    );
  }

  Future<void> _pickImage() async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 512,
        maxHeight: 512,
        imageQuality: 80,
      );

      if (image != null) {
        setState(() {
          _isUploadingImage = true;
        });

        if (kIsWeb) {
          // For web, read as bytes
          final bytes = await image.readAsBytes();
          setState(() {
            _selectedImageBytes = bytes;
            _selectedImage = null;
          });

          // Upload image
          await ref
              .read(profileProvider.notifier)
              .uploadProfileImage(bytes: bytes, fileName: image.name);
        } else {
          // For mobile, use File
          final file = File(image.path);
          setState(() {
            _selectedImage = file;
            _selectedImageBytes = null;
          });

          // Upload image
          await ref
              .read(profileProvider.notifier)
              .uploadProfileImage(file: file, fileName: image.name);
        }

        setState(() {
          _isUploadingImage = false;
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text(
                'Profile photo updated successfully!',
                style: TextStyle(fontFamily: 'Gilroy_Medium'),
              ),
              backgroundColor: NextSportzTheme.getAccentColor(
                ref.watch(theme_providers.isDarkModeProvider),
              ),
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
          );
        }
      }
    } catch (e) {
      setState(() {
        _isUploadingImage = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Failed to update profile photo: $e',
              style: const TextStyle(fontFamily: 'Gilroy_Medium'),
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    }
  }

  void _saveProfile() async {
    final currentProfile = ref.read(profileProvider).value;
    if (currentProfile != null) {
      // Only update date of birth if it has changed
      if (_selectedDateOfBirth != currentProfile.dateOfBirth) {
        final updatedProfile = currentProfile.copyWith(
          dateOfBirth: _selectedDateOfBirth,
        );

        await ref.read(profileProvider.notifier).updateProfile(updatedProfile);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text(
                'Date of birth updated successfully!',
                style: TextStyle(fontFamily: 'Gilroy_Medium'),
              ),
              backgroundColor: NextSportzTheme.getAccentColor(
                ref.watch(theme_providers.isDarkModeProvider),
              ),
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
          );
        }
      } else {
        // No changes made
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text(
                'No changes to save',
                style: TextStyle(fontFamily: 'Gilroy_Medium'),
              ),
              backgroundColor: Colors.orange,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
          );
        }
      }
    }
  }
}
