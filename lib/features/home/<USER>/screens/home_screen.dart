import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:animated_bottom_navigation_bar/animated_bottom_navigation_bar.dart';
import 'package:go_router/go_router.dart';
import 'package:nextsportz_v2/features/profile/presentation/widgets/player_rating_card.dart';
import '../../../../utils/responsive_utils.dart';
import '../../../../utils/responsive_wrapper.dart';
import '../../../../utils/mobile_wrapper.dart';
import 'package:nextsportz_v2/features/challenges/presentation/widgets/leaderboard_widget.dart';
import '../../../../core/config/theme.dart';
import '../../../../core/providers/theme_provider.dart' as theme_providers;
import '../../../profile/profile_providers.dart';
import '../../../profile/domain/entities/profile.dart';
import '../../../profile/presentation/screens/profile_settings_screen.dart';
import '../../../auth/presentation/logic/controller.dart';
import '../../../auth/presentation/screens/login_screen.dart';
import '../../../../core/local/token_storage.dart';

import '../../../teams/presentation/screens/my_teams_screen.dart';
import '../../../teams/presentation/widgets/team_leaderboard_widget.dart';
import '../../../teams/teams_providers.dart';
import '../../../challenges/presentation/screens/challenges_screen.dart';
import '../../../challenges/presentation/screens/my_matches_screen.dart';
import '../../../tournaments/presentation/screens/tournaments_screen.dart';
import '../../../venues/presentation/screens/venues_screen.dart';
import '../../../player/presentation/widgets/madsports_card_widget.dart';
import '../../../player/domain/entities/mad_sports_card.dart';
import '../../../player/presentation/screens/performance_stats_screen.dart';
import '../../../player/player_providers.dart';
import '../../../player/presentation/screens/player_profile_dialog_screen.dart';

import '../widgets/ad_carousel.dart';

class HomeScreen extends ConsumerStatefulWidget {
  const HomeScreen({Key? key}) : super(key: key);

  @override
  ConsumerState<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends ConsumerState<HomeScreen>
    with TickerProviderStateMixin {
  int _currentIndex = 2; // Default to center tab: Challenges

  late final List<Widget> _screens;

  final List<IconData> _icons = [
    Icons.dashboard,
    Icons.location_on,
    Icons.emoji_events,
    Icons.person,
  ];

  final List<String> _labels = [
    'Dashboard',
    'Venues',
    'Tournaments',
    'Profile',
  ];

  late AnimationController _fabAnimationController;
  late AnimationController _borderRadiusAnimationController;
  late Animation<double> fabAnimation;
  late Animation<double> borderRadiusAnimation;
  late CurvedAnimation fabCurve;
  late CurvedAnimation borderRadiusCurve;

  @override
  void initState() {
    super.initState();

    // Initialize screens with callback
    _screens = [
      DashboardTab(onChallengesTap: _navigateToChallenges),
      const VenuesScreen(),
      const ChallengesScreen(),
      const TournamentsScreen(),
      const ProfileTab(),
    ];

    _fabAnimationController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _borderRadiusAnimationController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    fabCurve = CurvedAnimation(
      parent: _fabAnimationController,
      curve: const Interval(0.5, 1.0, curve: Curves.fastOutSlowIn),
    );
    borderRadiusCurve = CurvedAnimation(
      parent: _borderRadiusAnimationController,
      curve: const Interval(0.5, 1.0, curve: Curves.fastOutSlowIn),
    );

    fabAnimation = Tween<double>(begin: 0, end: 1).animate(fabCurve);
    borderRadiusAnimation = Tween<double>(
      begin: 0,
      end: 1,
    ).animate(borderRadiusCurve);

    Future.delayed(
      const Duration(milliseconds: 500),
      () => _fabAnimationController.forward(),
    );
    Future.delayed(
      const Duration(milliseconds: 500),
      () => _borderRadiusAnimationController.forward(),
    );
  }

  void _navigateToChallenges() {
    _fabAnimationController.reset();
    _borderRadiusAnimationController.reset();
    _borderRadiusAnimationController.forward();
    _fabAnimationController.forward();
    setState(() => _currentIndex = 2); // Navigate to Challenges tab
  }

  @override
  void dispose() {
    _fabAnimationController.dispose();
    _borderRadiusAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final primaryColor = NextSportzTheme.getPrimaryColor(isDark);
    final accentColor = NextSportzTheme.getAccentColor(isDark);
    final greyColor = NextSportzTheme.getGreyColor(isDark);

    return Scaffold(
      backgroundColor: primaryColor,
      body: SafeArea(child: _screens[_currentIndex]),
      floatingActionButton: ScaleTransition(
        scale: fabAnimation,
        child: FloatingActionButton(
          onPressed: _navigateToChallenges,
          tooltip: 'Challenge',
          backgroundColor: accentColor,
          child: Icon(
            Icons.flash_on,
            color: Colors.white,
            size: ResponsiveUtils.getResponsiveIconSize(24),
          ),
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
      bottomNavigationBar: AnimatedBottomNavigationBar.builder(
        itemCount: _icons.length,
        tabBuilder: (int index, bool isActive) {
          final color = isActive ? accentColor : greyColor.withOpacity(0.8);
          return Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                _icons[index],
                size: ResponsiveUtils.getResponsiveIconSize(24),
                color: color,
              ),
              SizedBox(height: ResponsiveUtils.responsiveXsSpacing),
              Text(
                _labels[index],
                style: ResponsiveUtils.getResponsiveCaptionTextStyle(
                  fontSize: ResponsiveUtils.responsiveXsFontSize,
                  color: color,
                  fontFamily: 'Gilroy_Medium',
                ),
              ),
            ],
          );
        },
        backgroundColor: primaryColor,
        activeIndex:
            _currentIndex == 2
                ? -1
                : (_currentIndex > 2 ? _currentIndex - 1 : _currentIndex),
        splashColor: accentColor,
        notchAndCornersAnimation: borderRadiusAnimation,
        splashSpeedInMilliseconds: 300,
        notchSmoothness: NotchSmoothness.softEdge,
        gapLocation: GapLocation.center,
        leftCornerRadius: ResponsiveUtils.getResponsiveBorderRadius(),
        rightCornerRadius: ResponsiveUtils.getResponsiveBorderRadius(),
        onTap:
            (index) => setState(() {
              // Map the tapped index correctly considering the center gap at index 2
              if (index >= 2) {
                _currentIndex = index + 1; // Skip the challenges tab (index 2)
              } else {
                _currentIndex = index;
              }
            }),
        shadow: BoxShadow(
          offset: ResponsiveUtils.getResponsiveShadowOffset(),
          blurRadius: ResponsiveUtils.getResponsiveShadowBlur(),
          spreadRadius: 0.5,
          color: accentColor.withOpacity(0.2),
        ),
      ),
    );
  }
}

class DashboardTab extends ConsumerWidget {
  final VoidCallback? onChallengesTap;

  const DashboardTab({Key? key, this.onChallengesTap}) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // demo data
    final demoCard = MadSportsCard(
      playerId: 'u1',
      playerName: 'Alex Subedi',
      age: 24,
      position: 'FW',
      countryFlag: '🇳🇵',
      jerseyNumber: 9,
      season: '24/25',
      rarity: 'Bronze',
      profileImageUrl:
          'https://images.unsplash.com/photo-1502877338535-766e1452684a',
      shareableText: 'Catch my NextSportz player card! #NextSportz',
    );

    return ResponsiveWrapper(
      useScrollView: true,
      child: Padding(
        padding: ResponsiveUtils.getResponsivePadding(horizontal: 16),
        child: ResponsiveColumn(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: ResponsiveUtils.getResponsiveSafeAreaTop()),

            // 1) NextSportz Card
            MadSportsCardWidget(card: demoCard),

            SizedBox(height: ResponsiveUtils.responsiveLgSpacing),

            // 2) Advertisement carousel banner
            Text(
              'Sponsored',
              style: ResponsiveUtils.getResponsiveBodyTextStyle(
                fontSize: ResponsiveUtils.responsiveSmFontSize,
                color: Colors.white70,
                fontFamily: 'Gilroy_Medium',
              ),
            ),
            SizedBox(height: ResponsiveUtils.responsiveSmSpacing),
            const AdCarousel(
              imageUrls: [
                'assets/images/template_1.jpg',
                'assets/images/template_2.jpg',
                'assets/images/template_3.jpg',
              ],
            ),

            SizedBox(height: ResponsiveUtils.responsiveLgSpacing),

            // 3) Top teams (with logos)
            Consumer(
              builder: (context, ref, child) {
                final teamsAsync = ref.watch(teamLeaderboardProvider);
                return teamsAsync.when(
                  data:
                      (teams) => TeamLeaderboardWidget(
                        teams: teams,
                        onViewMore: () {
                          // Navigate to team list page
                          context.go('/teams');
                        },
                      ),
                  loading:
                      () => const TeamLeaderboardWidget(
                        teams: [],
                        isLoading: true,
                      ),
                  error:
                      (error, stackTrace) => TeamLeaderboardWidget(
                        teams: [],
                        error: error.toString(),
                      ),
                );
              },
            ),

            SizedBox(height: ResponsiveUtils.responsiveLgSpacing),

            // CTA to Challenges screen (clickbait style)
            _challengeCTA(context, ref),

            SizedBox(height: ResponsiveUtils.responsiveLgSpacing),

            // 4) Activities
            Text(
              'Recent Activity',
              style: ResponsiveUtils.getResponsiveHeadingTextStyle(
                fontSize: ResponsiveUtils.responsiveXlFontSize,
                color: Colors.white,
                fontFamily: 'Gilroy_Medium',
              ),
            ),
            SizedBox(height: ResponsiveUtils.responsiveMdSpacing),
            _buildActivityItem(
              'Match Challenge Accepted',
              'You accepted a 5v5 challenge',
              '2 hours ago',
              ref,
            ),
            _buildActivityItem(
              'Tournament Registration',
              'Successfully registered for Summer Cup',
              '1 day ago',
              ref,
            ),
            _buildActivityItem(
              'Team Invitation',
              'You were invited to join Team Alpha',
              '3 days ago',
              ref,
            ),
            SizedBox(
              height: ResponsiveUtils.responsiveLgSpacing,
            ), // Bottom spacing
          ],
        ),
      ),
    );
  }

  Widget _challengeCTA(BuildContext context, WidgetRef ref) {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final accentColor = NextSportzTheme.getAccentColor(isDark);
    final accentSecondaryColor =
        isDark
            ? NextSportzTheme.darkAccentSecondary
            : NextSportzTheme.lightAccentSecondary;

    return GestureDetector(
      onTap: onChallengesTap,
      child: Container(
        padding: EdgeInsets.all(ResponsiveUtils.responsiveMdSpacing),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(
            ResponsiveUtils.getResponsiveBorderRadius(),
          ),
          gradient: LinearGradient(
            colors: [accentColor, accentSecondaryColor],
            begin: Alignment.centerLeft,
            end: Alignment.centerRight,
          ),
          boxShadow: [
            BoxShadow(
              color: accentColor.withOpacity(0.2),
              blurRadius: ResponsiveUtils.getResponsiveShadowBlur() * 2,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Icon(Icons.flash_on, color: Colors.white, size: 28),
            ),
            const SizedBox(width: 16),
            const Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Instant Challenge',
                    style: TextStyle(
                      fontFamily: 'Gilroy_Bold',
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 4),
                  Text(
                    'Post now. Get accepted fast. Win your streak! Tap to begin →',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 13,
                      fontFamily: 'Gilroy_Medium',
                    ),
                  ),
                ],
              ),
            ),
            const Icon(Icons.chevron_right, color: Colors.white),
          ],
        ),
      ),
    );
  }

  Widget _buildActivityItem(
    String title,
    String subtitle,
    String time,
    WidgetRef ref,
  ) {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final secondaryColor = NextSportzTheme.getSecondaryColor(isDark);
    final accentColor = NextSportzTheme.getAccentColor(isDark);
    final greyColor = NextSportzTheme.getGreyColor(isDark);

    return Container(
      margin: EdgeInsets.only(bottom: ResponsiveUtils.responsiveMdSpacing),
      padding: EdgeInsets.all(ResponsiveUtils.responsiveMdSpacing),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(
          ResponsiveUtils.getResponsiveBorderRadius(),
        ),
        color: secondaryColor.withOpacity(0.5),
        border: Border.all(color: Colors.white.withOpacity(0.1), width: 1),
      ),
      child: Row(
        children: [
          Container(
            width: ResponsiveUtils.getResponsiveBadgeSize(),
            height: ResponsiveUtils.getResponsiveBadgeSize(),
            decoration: BoxDecoration(
              color: accentColor,
              shape: BoxShape.circle,
            ),
          ),
          SizedBox(width: ResponsiveUtils.responsiveMdSpacing),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: ResponsiveUtils.getResponsiveBodyTextStyle(
                    fontSize: ResponsiveUtils.responsiveMdFontSize,
                    color: Colors.white,
                    fontFamily: 'Gilroy_Medium',
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: ResponsiveUtils.responsiveXsSpacing),
                Text(
                  subtitle,
                  style: ResponsiveUtils.getResponsiveBodyTextStyle(
                    fontSize: ResponsiveUtils.responsiveSmFontSize,
                    color: greyColor,
                    fontFamily: 'Gilroy_Medium',
                  ),
                ),
              ],
            ),
          ),
          Text(
            time,
            style: ResponsiveUtils.getResponsiveCaptionTextStyle(
              fontSize: ResponsiveUtils.responsiveXsFontSize,
              color: greyColor,
              fontFamily: 'Gilroy_Medium',
            ),
          ),
        ],
      ),
    );
  }
}

// Matches tab is not used currently
class MatchesTab extends StatelessWidget {
  const MatchesTab({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Text('Matches Screen', style: TextStyle(color: Colors.white)),
    );
  }
}

// Deprecated placeholder removed. See features/tournaments/presentation/screens/tournaments_screen.dart for actual UI.

class ProfileTab extends ConsumerStatefulWidget {
  const ProfileTab({Key? key}) : super(key: key);

  @override
  ConsumerState<ProfileTab> createState() => _ProfileTabState();
}

class _ProfileTabState extends ConsumerState<ProfileTab> {
  @override
  void initState() {
    super.initState();
    // Load profile data when the tab is first opened
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(profileProvider.notifier).loadProfile();
    });
  }

  @override
  Widget build(BuildContext context) {
    final profileAsync = ref.watch(profileProvider);

    return ResponsiveWrapper(
      useScrollView: true,
      child: Padding(
        padding: ResponsiveUtils.getResponsivePadding(horizontal: 16),
        child: ResponsiveColumn(
          children: [
            SizedBox(height: ResponsiveUtils.getResponsiveSafeAreaTop()),

            // Combined Profile Header and Player Profile Card
            _buildCombinedProfileSection(context, profileAsync),

            const SizedBox(height: 12),

            // Player Rating Card
            const PlayerRatingCard(),

            const SizedBox(height: 12),

            // Menu Items
            _buildMenuSection(context),
            SizedBox(
              height: ResponsiveUtils.responsiveLgSpacing,
            ), // Bottom spacing
          ],
        ),
      ),
    );
  }

  Widget _buildCombinedProfileSection(
    BuildContext context,
    AsyncValue<Profile?> profileAsync,
  ) {
    final playerStatsAsync = ref.watch(currentPlayerStatsProvider);
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final accentColor = NextSportzTheme.getAccentColor(isDark);
    final accentSecondaryColor =
        isDark
            ? NextSportzTheme.darkAccentSecondary
            : NextSportzTheme.lightAccentSecondary;

    return GestureDetector(
      onTap: () async {
        // Get current player ID and navigate to player profile dialog
        final playerId = await ref.read(currentPlayerIdProvider.future);
        if (context.mounted) {
          // For now, keep as modal navigation since we don't have a dedicated route
          Navigator.of(context).push(
            MaterialPageRoute(
              builder:
                  (context) => PlayerProfileDialogScreen(playerId: playerId),
            ),
          );
        }
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              accentColor.withOpacity(0.8),
              accentSecondaryColor.withOpacity(0.8),
            ],
          ),
          boxShadow: [
            BoxShadow(
              color: accentColor.withOpacity(0.3),
              blurRadius: 15,
              offset: const Offset(0, 8),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            children: [
              // Profile Header Section
              Row(
                children: [
                  // Profile Image
                  Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(color: Colors.white, width: 3),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.2),
                          blurRadius: 10,
                          offset: const Offset(0, 5),
                        ),
                      ],
                    ),
                    child: ClipOval(
                      child: Image.asset(
                        "assets/images/profile.png",
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: Colors.white.withOpacity(0.2),
                            ),
                            child: const Icon(
                              Icons.person,
                              size: 40,
                              color: Colors.white,
                            ),
                          );
                        },
                      ),
                    ),
                  ),

                  const SizedBox(width: 16),

                  // User Info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        profileAsync.when(
                          data:
                              (profile) => Text(
                                profile?.name ?? "Loading...",
                                style: const TextStyle(
                                  fontFamily: 'Gilroy_Bold',
                                  color: Colors.white,
                                  fontSize: 20,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                          loading:
                              () => const Text(
                                "Loading...",
                                style: TextStyle(
                                  fontFamily: 'Gilroy_Bold',
                                  color: Colors.white,
                                  fontSize: 20,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                          error:
                              (error, stack) => const Text(
                                "Error loading profile",
                                style: TextStyle(
                                  fontFamily: 'Gilroy_Bold',
                                  color: Colors.white,
                                  fontSize: 20,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                        ),
                        const SizedBox(height: 4),
                        profileAsync.when(
                          data:
                              (profile) => Text(
                                profile?.role == 'player'
                                    ? "Professional Player"
                                    : "User",
                                style: TextStyle(
                                  fontFamily: 'Gilroy_Medium',
                                  color: Colors.white.withOpacity(0.8),
                                  fontSize: 14,
                                ),
                              ),
                          loading:
                              () => Text(
                                "Loading...",
                                style: TextStyle(
                                  fontFamily: 'Gilroy_Medium',
                                  color: Colors.white.withOpacity(0.8),
                                  fontSize: 14,
                                ),
                              ),
                          error:
                              (error, stack) => Text(
                                "Error",
                                style: TextStyle(
                                  fontFamily: 'Gilroy_Medium',
                                  color: Colors.white.withOpacity(0.8),
                                  fontSize: 14,
                                ),
                              ),
                        ),
                        const SizedBox(height: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 6,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: const Text(
                            "Premium Member",
                            style: TextStyle(
                              fontFamily: 'Gilroy_Medium',
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  // Action Buttons
                  Row(
                    children: [
                      // View Public Preview Button
                      IconButton(
                        onPressed: () async {
                          final playerId = await ref.read(
                            currentPlayerIdProvider.future,
                          );
                          if (context.mounted) {
                            context.go("/players/$playerId");
                          }
                        },
                        icon: Container(
                          padding: const EdgeInsets.all(4),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const Icon(
                            Icons.visibility,
                            color: Colors.white,
                            size: 12,
                          ),
                        ),
                      ),
                      // Edit Button
                      IconButton(
                        onPressed: () {
                          context.go("/profile-settings");
                        },
                        icon: Container(
                          padding: const EdgeInsets.all(4),

                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const Icon(
                            Icons.edit,
                            color: Colors.white,
                            size: 12,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),

              // Performance Stats Section - Creative Design
              const SizedBox(height: 16),

              // Stats Grid with creative layout
              playerStatsAsync.when(
                data: (stats) => _buildCreativeStatsGrid(stats),
                loading: () => _buildCreativeStatsGrid({}),
                error: (error, stack) => _buildCreativeStatsGrid({}),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCreativeStatsGrid(Map<String, dynamic> stats) {
    final totalChallenges = stats['totalChallenges'] ?? 0;
    final challengesWon = stats['challengesWon'] ?? 0;
    final totalWagerWon = stats['totalWagerWon'] ?? 0;
    final winRate =
        totalChallenges > 0 ? (challengesWon / totalChallenges * 100) : 0.0;

    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final secondaryColor = NextSportzTheme.getSecondaryColor(isDark);

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        color: secondaryColor.withOpacity(0.5),
        border: Border.all(color: Colors.white.withOpacity(0.1), width: 1),
      ),
      child: Row(
        children: [
          // Challenges Stat
          Expanded(
            child: _buildCreativeStatItemInContainer(
              'Challenges',
              totalChallenges.toString(),
              Icons.flash_on,
              Colors.orange,
            ),
          ),
          // Divider
          Container(height: 60, width: 1, color: Colors.white.withOpacity(0.1)),
          // Wins Stat
          Expanded(
            child: _buildCreativeStatItemInContainer(
              'Wins',
              challengesWon.toString(),
              Icons.emoji_events,
              Colors.amber,
            ),
          ),
          // Divider
          Container(height: 60, width: 1, color: Colors.white.withOpacity(0.1)),
          // Wager Stat
          Expanded(
            child: _buildCreativeStatItemInContainer(
              'Wager Won',
              '₹${(totalWagerWon / 1000).toStringAsFixed(1)}k',
              Icons.monetization_on,
              Colors.green,
            ),
          ),
          // Divider
          Container(height: 60, width: 1, color: Colors.white.withOpacity(0.1)),
          // Win Rate Stat
          Expanded(
            child: _buildCreativeStatItemInContainer(
              'Win Rate',
              '${winRate.toStringAsFixed(1)}%',
              Icons.trending_up,
              Colors.blue,
            ),
          ),
          // Chevron Right Icon
          Padding(
            padding: const EdgeInsets.only(right: 8),
            child: Icon(
              Icons.chevron_right,
              color: Colors.white.withOpacity(0.6),
              size: 24,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCreativeStatItemInContainer(
    String label,
    String value,
    IconData icon,
    Color accentColor,
  ) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 12),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Compact icon and value row
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, color: accentColor, size: 14),
              const SizedBox(width: 4),
              Expanded(
                child: Text(
                  value,
                  style: TextStyle(
                    fontFamily: 'Gilroy_Bold',
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),

          const SizedBox(height: 4),

          // Label
          Text(
            label,
            style: TextStyle(
              fontFamily: 'Gilroy_Medium',
              color: Colors.white.withOpacity(0.7),
              fontSize: 10,
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withOpacity(0.2), width: 1),
      ),
      child: Column(
        children: [
          Icon(icon, color: Colors.white, size: 20),
          const SizedBox(height: 8),
          Text(
            value,
            style: const TextStyle(
              fontFamily: 'Gilroy_Bold',
              color: Colors.white,
              fontSize: 16,
            ),
          ),
          Text(
            label,
            style: TextStyle(
              fontFamily: 'Gilroy_Medium',
              color: Colors.white.withOpacity(0.8),
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMenuSection(BuildContext context) {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final secondaryColor = NextSportzTheme.getSecondaryColor(isDark);

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        color: secondaryColor.withOpacity(0.5),
        border: Border.all(color: Colors.white.withOpacity(0.1), width: 1),
      ),
      child: Column(
        children: [
          _buildMenuItem(context, "Matches", Icons.sports_soccer, () {
            context.go('/my-matches');
          }),
          _buildDivider(),
          _buildMenuItem(context, "My Teams", Icons.group, () {
            context.go('/my-teams');
          }),
          _buildDivider(),
          _buildMenuItem(context, "Booked Venues", Icons.location_on, () {
            // TODO: Navigate to venues
          }),
          _buildDivider(),
          _buildMenuItem(context, "Settings", Icons.settings, () {
            context.go('/profile-settings');
          }),
          _buildDivider(),
          _buildMenuItem(context, "Help & Support", Icons.help_outline, () {
            // TODO: Navigate to help
          }),
          _buildDivider(),
          _buildMenuItem(context, "Logout", Icons.logout, () {
            _showLogoutDialog(context);
          }, isDestructive: true),
        ],
      ),
    );
  }

  Widget _buildMenuItem(
    BuildContext context,
    String title,
    IconData icon,
    VoidCallback onTap, {
    bool isDestructive = false,
  }) {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final accentColor = NextSportzTheme.getAccentColor(isDark);
    final greyColor = NextSportzTheme.getGreyColor(isDark);

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(20),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color:
                    isDestructive
                        ? Colors.red.withOpacity(0.2)
                        : accentColor.withOpacity(0.2),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Icon(
                icon,
                color: isDestructive ? Colors.red : accentColor,
                size: 20,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Text(
                title,
                style: TextStyle(
                  fontFamily: 'Gilroy_Medium',
                  color: isDestructive ? Colors.red : Colors.white,
                  fontSize: 16,
                ),
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: isDestructive ? Colors.red : greyColor,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDivider() {
    return Divider(
      height: 1,
      color: Colors.white.withOpacity(0.1),
      indent: 60,
      endIndent: 20,
    );
  }

  void _showLogoutDialog(BuildContext context) {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final secondaryColor = NextSportzTheme.getSecondaryColor(isDark);
    final greyColor = NextSportzTheme.getGreyColor(isDark);

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: secondaryColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: const Text(
            "Logout",
            style: TextStyle(
              fontFamily: 'Gilroy_Bold',
              color: Colors.white,
              fontSize: 18,
            ),
          ),
          content: const Text(
            "Are you sure you want to logout?",
            style: TextStyle(
              fontFamily: 'Gilroy_Medium',
              color: Colors.white,
              fontSize: 14,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                "Cancel",
                style: TextStyle(fontFamily: 'Gilroy_Medium', color: greyColor),
              ),
            ),
            TextButton(
              onPressed: () async {
                Navigator.of(context).pop();
                await _performLogout();
              },
              child: Text(
                "Logout",
                style: TextStyle(
                  fontFamily: 'Gilroy_Medium',
                  color: Colors.red,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Future<void> _performLogout() async {
    try {
      // First, clear local storage immediately for better UX
      final tokenStorage = ref.read(tokenStorageProvider);
      await tokenStorage.clearTokens();

      // Try to call the logout API (but don't block on it since it may fail)
      try {
        // This will also update the auth state properly
        await ref.read(authNotifierProvider.notifier).logout();
      } catch (apiError) {
        // If API fails, manually set auth state to unauthenticated
        // Since we cleared local storage, user should be logged out
        print('Logout API failed (ignored): $apiError');
      }

      // Navigate to login screen
      if (mounted) {
        Navigator.of(context).pushAndRemoveUntil(
          MaterialPageRoute(builder: (context) => const LoginScreen()),
          (Route<dynamic> route) => false,
        );
      }
    } catch (e) {
      // Show error but still try to navigate to login
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Logout error: ${e.toString()}',
              style: const TextStyle(fontFamily: 'Gilroy_Medium'),
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );

        // Still navigate to login even if there was an error
        Navigator.of(context).pushAndRemoveUntil(
          MaterialPageRoute(builder: (context) => const LoginScreen()),
          (Route<dynamic> route) => false,
        );
      }
    }
  }
}

class _CreateChallengeSheet extends ConsumerStatefulWidget {
  final ScrollController scrollController;
  const _CreateChallengeSheet({required this.scrollController});

  @override
  ConsumerState<_CreateChallengeSheet> createState() =>
      _CreateChallengeSheetState();
}

class _CreateChallengeSheetState extends ConsumerState<_CreateChallengeSheet> {
  String _matchType = '5v5';
  final TextEditingController _locationCtrl = TextEditingController();
  final TextEditingController _timeCtrl = TextEditingController();
  final TextEditingController _futsalCtrl = TextEditingController();
  bool _losersPay = true;
  double _extraMoney = 0;

  @override
  void dispose() {
    _locationCtrl.dispose();
    _timeCtrl.dispose();
    _futsalCtrl.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      top: false,
      child: SingleChildScrollView(
        controller: widget.scrollController,
        padding: const EdgeInsets.fromLTRB(16, 12, 16, 24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Center(
              child: Container(
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
            ),
            const SizedBox(height: 12),
            const Text(
              'Place a Challenge',
              style: TextStyle(
                fontFamily: 'Gilroy_Bold',
                color: Colors.white,
                fontSize: 18,
              ),
            ),
            const SizedBox(height: 16),

            // Match type
            const Text('Match Type', style: TextStyle(color: Colors.white70)),
            const SizedBox(height: 8),
            Wrap(
              spacing: 10,
              children:
                  ['5v5', '7v7', '11v11']
                      .map(
                        (t) => ChoiceChip(
                          label: Text(
                            t,
                            style: TextStyle(
                              color:
                                  _matchType == t
                                      ? Colors.white
                                      : Colors.black87,
                              fontWeight:
                                  _matchType == t
                                      ? FontWeight.bold
                                      : FontWeight.normal,
                              fontFamily: 'Gilroy_Medium',
                            ),
                          ),
                          selected: _matchType == t,
                          onSelected: (_) => setState(() => _matchType = t),
                          selectedColor: NextSportzTheme.getAccentColor(
                            ref.watch(theme_providers.isDarkModeProvider),
                          ),
                          backgroundColor: Colors.white.withOpacity(0.9),
                          checkmarkColor: Colors.white,
                          side: BorderSide(
                            color:
                                _matchType == t
                                    ? NextSportzTheme.getAccentColor(
                                      ref.watch(
                                        theme_providers.isDarkModeProvider,
                                      ),
                                    )
                                    : Colors.white.withOpacity(0.3),
                            width: 1.5,
                          ),
                        ),
                      )
                      .toList(),
            ),

            const SizedBox(height: 16),
            // Preferred locations
            const Text(
              'Preferred Location',
              style: TextStyle(color: Colors.white70),
            ),
            const SizedBox(height: 8),
            _buildField(
              _locationCtrl,
              hint: 'Area or venue (e.g., Dhumbarahi) ',
              icon: Icons.place,
            ),

            const SizedBox(height: 16),
            // Time
            const Text(
              'Preferred Timing',
              style: TextStyle(color: Colors.white70),
            ),
            const SizedBox(height: 8),
            _buildField(
              _timeCtrl,
              hint: 'e.g., Tonight 7-9 PM or Sat Morning',
              icon: Icons.schedule,
            ),

            const SizedBox(height: 16),
            // Preferred futsal
            const Text(
              'Preferred Futsal (optional)',
              style: TextStyle(color: Colors.white70),
            ),
            const SizedBox(height: 8),
            _buildField(
              _futsalCtrl,
              hint: 'e.g., Bhatbhateni Futsal',
              icon: Icons.sports_soccer,
            ),

            const SizedBox(height: 16),
            // Wager
            const Text('Wager', style: TextStyle(color: Colors.white70)),
            const SizedBox(height: 8),
            Row(
              children: [
                Switch(
                  value: _losersPay,
                  activeColor: NextSportzTheme.getAccentColor(
                    ref.watch(theme_providers.isDarkModeProvider),
                  ),
                  onChanged: (v) => setState(() => _losersPay = v),
                ),
                const SizedBox(width: 8),
                const Text(
                  'Losers pay',
                  style: TextStyle(color: Colors.white70),
                ),
              ],
            ),
            const SizedBox(height: 6),
            Row(
              children: [
                const Icon(Icons.attach_money, color: Colors.white70, size: 18),
                const SizedBox(width: 6),
                Expanded(
                  child: Slider(
                    min: 0,
                    max: 2000,
                    divisions: 20,
                    value: _extraMoney,
                    activeColor: NextSportzTheme.getAccentColor(
                      ref.watch(theme_providers.isDarkModeProvider),
                    ),
                    label: 'NPR ${_extraMoney.toStringAsFixed(0)}',
                    onChanged: (v) => setState(() => _extraMoney = v),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 10,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Text(
                    'NPR ${_extraMoney.toStringAsFixed(0)}',
                    style: const TextStyle(color: Colors.white),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 20),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                icon: const Icon(Icons.flash_on, color: Colors.white),
                label: const Text(
                  'Place Challenge',
                  style: TextStyle(color: Colors.white),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: NextSportzTheme.getAccentColor(
                    ref.watch(theme_providers.isDarkModeProvider),
                  ),
                  padding: const EdgeInsets.symmetric(vertical: 14),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(14),
                  ),
                ),
                onPressed: () {
                  Navigator.of(context).pop();
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Challenge posted!'),
                      behavior: SnackBarBehavior.floating,
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildField(
    TextEditingController ctrl, {
    required String hint,
    required IconData icon,
  }) {
    return TextField(
      controller: ctrl,
      style: const TextStyle(color: Colors.white),
      decoration: InputDecoration(
        hintText: hint,
        hintStyle: const TextStyle(color: Colors.white54),
        prefixIcon: Icon(icon, color: Colors.white70),
        filled: true,
        fillColor: Colors.white.withOpacity(0.08),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.white.withOpacity(0.1)),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: NextSportzTheme.getAccentColor(
              ref.watch(theme_providers.isDarkModeProvider),
            ),
          ),
        ),
      ),
    );
  }
}
