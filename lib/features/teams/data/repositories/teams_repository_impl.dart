import 'package:fpdart/fpdart.dart';
import '../../../../core/networking/app_error.dart';
import '../../../../core/networking/exception.dart';
import '../../../../core/models/paginated_response.dart';
import '../../domain/entities/team.dart';
import '../../domain/repositories/teams_repository.dart';
import '../datasources/teams_datasource.dart';

/// Repository implementation for teams operations
class TeamsRepositoryImpl implements TeamsRepository {
  final TeamsDatasource _dataSource;

  TeamsRepositoryImpl(this._dataSource);

  @override
  Future<Either<AppError, List<TeamSearchItem>>> getMyTeams() async {
    try {
      final teams = await _dataSource.getMyTeams();
      return Right(teams);
    } on DioExceptionHandle catch (e) {
      return Left(AppError(e.message));
    } catch (e) {
      return Left(AppError(e.toString()));
    }
  }

  @override
  Future<Either<AppError, Team>> getTeamById(String teamId) async {
    try {
      final team = await _dataSource.getTeamById(teamId);
      return Right(team);
    } on DioExceptionHandle catch (e) {
      return Left(AppError(e.message));
    } catch (e) {
      return Left(AppError(e.toString()));
    }
  }

  @override
  Future<Either<AppError, Team>> createTeam({
    required String name,
    required String description,
    String? logo,
    String? slogan,
  }) async {
    try {
      final team = await _dataSource.createTeam(
        name: name,
        description: description,
        logo: logo,
        slogan: slogan,
      );
      return Right(team);
    } on DioExceptionHandle catch (e) {
      return Left(AppError(e.message));
    } catch (e) {
      return Left(AppError(e.toString()));
    }
  }

  @override
  Future<Either<AppError, void>> updateTeam({
    required String teamId,
    String? name,
    String? description,
    String? logo,
    String? slogan,
  }) async {
    try {
      await _dataSource.updateTeam(
        teamId: teamId,
        name: name,
        description: description,
        logo: logo,
        slogan: slogan,
      );
      return const Right(null);
    } on DioExceptionHandle catch (e) {
      return Left(AppError(e.message));
    } catch (e) {
      return Left(AppError(e.toString()));
    }
  }

  @override
  Future<Either<AppError, void>> deleteTeam(String teamId) async {
    try {
      await _dataSource.deleteTeam(teamId);
      return const Right(null);
    } on DioExceptionHandle catch (e) {
      return Left(AppError(e.message));
    } catch (e) {
      return Left(AppError(e.toString()));
    }
  }

  @override
  Future<Either<AppError, void>> invitePlayer({
    required String teamId,
    required String playerId,
    required String role,
  }) async {
    try {
      await _dataSource.invitePlayer(
        teamId: teamId,
        playerId: playerId,
        role: role,
      );
      return const Right(null);
    } on DioExceptionHandle catch (e) {
      return Left(AppError(e.message));
    } catch (e) {
      return Left(AppError(e.toString()));
    }
  }

  @override
  Future<Either<AppError, void>> acceptInvitation(String invitationId) async {
    try {
      await _dataSource.acceptInvitation(invitationId);
      return const Right(null);
    } on DioExceptionHandle catch (e) {
      return Left(AppError(e.message));
    } catch (e) {
      return Left(AppError(e.toString()));
    }
  }

  @override
  Future<Either<AppError, void>> declineInvitation(String invitationId) async {
    try {
      await _dataSource.declineInvitation(invitationId);
      return const Right(null);
    } on DioExceptionHandle catch (e) {
      return Left(AppError(e.message));
    } catch (e) {
      return Left(AppError(e.toString()));
    }
  }

  @override
  Future<Either<AppError, void>> removeMember({
    required String teamId,
    required String memberId,
  }) async {
    try {
      await _dataSource.removeMember(teamId: teamId, memberId: memberId);
      return const Right(null);
    } on DioExceptionHandle catch (e) {
      return Left(AppError(e.message));
    } catch (e) {
      return Left(AppError(e.toString()));
    }
  }

  @override
  Future<Either<AppError, void>> updateMemberRole({
    required String teamId,
    required String memberId,
    required String role,
  }) async {
    try {
      await _dataSource.updateMemberRole(
        teamId: teamId,
        memberId: memberId,
        role: role,
      );
      return const Right(null);
    } on DioExceptionHandle catch (e) {
      return Left(AppError(e.message));
    } catch (e) {
      return Left(AppError(e.toString()));
    }
  }

  @override
  Future<Either<AppError, List<TeamInvitation>>> getPendingInvitations() async {
    try {
      final invitations = await _dataSource.getPendingInvitations();
      return Right(invitations);
    } on DioExceptionHandle catch (e) {
      return Left(AppError(e.message));
    } catch (e) {
      return Left(AppError(e.toString()));
    }
  }

  @override
  Future<Either<AppError, List<Team>>> getTeamsLeaderboard({int? limit}) async {
    try {
      final teams = await _dataSource.getTeamsLeaderboard(limit: limit);
      return Right(teams);
    } on DioExceptionHandle catch (e) {
      return Left(AppError(e.message));
    } catch (e) {
      return Left(AppError(e.toString()));
    }
  }

  @override
  Future<Either<AppError, List<Team>>> searchTeams({
    required String query,
    int? limit,
    int? offset,
  }) async {
    try {
      final teams = await _dataSource.searchTeams(
        query: query,
        limit: limit,
        offset: offset,
      );
      return Right(teams);
    } on DioExceptionHandle catch (e) {
      return Left(AppError(e.message));
    } catch (e) {
      return Left(AppError(e.toString()));
    }
  }

  @override
  Future<Either<AppError, List<Team>>> getAllTeams({
    int? limit,
    int? offset,
  }) async {
    try {
      final teams = await _dataSource.getAllTeams(limit: limit, offset: offset);
      return Right(teams);
    } on DioExceptionHandle catch (e) {
      return Left(AppError(e.message));
    } catch (e) {
      return Left(AppError(e.toString()));
    }
  }

  @override
  Future<Either<AppError, PaginatedResponse<TeamSearchItem>>>
  searchTeamsPaginated({String? query, int? page, int? pageSize}) async {
    try {
      final response = await _dataSource.searchTeamsPaginated(
        query: query,
        page: page,
        pageSize: pageSize,
      );
      return Right(response);
    } on DioExceptionHandle catch (e) {
      return Left(AppError(e.message));
    } catch (e) {
      return Left(AppError(e.toString()));
    }
  }

  @override
  Future<Either<AppError, PaginatedResponse<TeamSearchItem>>>
  getAllTeamsPaginated({int? page, int? pageSize}) async {
    try {
      final response = await _dataSource.getAllTeamsPaginated(
        page: page,
        pageSize: pageSize,
      );
      return Right(response);
    } on DioExceptionHandle catch (e) {
      return Left(AppError(e.message));
    } catch (e) {
      return Left(AppError(e.toString()));
    }
  }

  @override
  Future<Either<AppError, void>> joinTeamByCode({
    required String teamCode,
    required String playerId,
  }) async {
    try {
      await _dataSource.joinTeamByCode(teamCode: teamCode, playerId: playerId);
      return const Right(null);
    } on DioExceptionHandle catch (e) {
      return Left(AppError(e.message));
    } catch (e) {
      return Left(AppError(e.toString()));
    }
  }
}
