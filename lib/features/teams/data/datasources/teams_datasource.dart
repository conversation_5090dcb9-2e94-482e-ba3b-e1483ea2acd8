import '../../domain/entities/team.dart';
import '../dto/team_dto.dart';
import '../dto/team_request_dto.dart';
import '../dto/team_search_item_dto.dart';
import '../../../../core/models/paginated_response.dart';

/// Abstract class defining the contract for teams data operations
abstract class TeamsDatasource {
  /// Get all teams for the current user
  Future<List<TeamSearchItem>> getMyTeams();

  /// Get a specific team by ID
  Future<Team> getTeamById(String teamId);

  /// Create a new team
  Future<Team> createTeam({
    required String name,
    required String description,
    String? logo,
    String? slogan,
  });

  /// Update an existing team
  Future<void> updateTeam({
    required String teamId,
    String? name,
    String? description,
    String? logo,
    String? slogan,
  });

  /// Delete a team
  Future<void> deleteTeam(String teamId);

  /// Invite a player to join the team
  Future<void> invitePlayer({
    required String teamId,
    required String playerId,
    required String role,
  });

  /// Accept a team invitation
  Future<void> acceptInvitation(String invitationId);

  /// Decline a team invitation
  Future<void> declineInvitation(String invitationId);

  /// Remove a member from the team
  Future<void> removeMember({required String teamId, required String memberId});

  /// Update a member's role in the team
  Future<void> updateMemberRole({
    required String teamId,
    required String memberId,
    required String role,
  });

  /// Get pending invitations for the current user
  Future<List<TeamInvitation>> getPendingInvitations();

  /// Upload team logo
  Future<String> uploadTeamLogo(dynamic imageFile);

  /// Get teams leaderboard
  Future<List<Team>> getTeamsLeaderboard({int? limit});

  /// Search teams by name or other criteria
  Future<List<Team>> searchTeams({
    required String query,
    int? limit,
    int? offset,
  });

  /// Get all teams (public)
  Future<List<Team>> getAllTeams({int? limit, int? offset});

  /// Search teams with pagination
  Future<PaginatedResponse<TeamSearchItem>> searchTeamsPaginated({
    String? query,
    int? page,
    int? pageSize,
  });

  /// Get all teams with pagination (public)
  Future<PaginatedResponse<TeamSearchItem>> getAllTeamsPaginated({
    int? page,
    int? pageSize,
  });

  /// Join a team using team code
  Future<void> joinTeamByCode({
    required String teamCode,
    required String playerId,
  });
}
