import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'dart:typed_data';
import '../../../../core/config/theme.dart';
import '../../../../core/providers/theme_provider.dart' as theme_providers;
import '../../../../core/image_upload/image_upload_providers.dart';
import '../../teams_providers.dart';

class CreateTeamScreen extends ConsumerStatefulWidget {
  const CreateTeamScreen({super.key});

  @override
  ConsumerState<CreateTeamScreen> createState() => _CreateTeamScreenState();
}

class _CreateTeamScreenState extends ConsumerState<CreateTeamScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _sloganController = TextEditingController();
  final _imagePicker = ImagePicker();
  bool _isLoading = false;
  File? _selectedImage;
  Uint8List? _selectedImageBytes;

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _sloganController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final primaryColor = NextSportzTheme.getPrimaryColor(isDark);
    final accentColor = NextSportzTheme.getAccentColor(isDark);

    return Scaffold(
      backgroundColor: primaryColor,
      appBar: AppBar(
        backgroundColor: primaryColor,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: const Text(
          'Create Team',
          style: TextStyle(
            fontFamily: 'Gilroy_Bold',
            color: Colors.white,
            fontSize: 18,
          ),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Team Logo Section
              Center(
                child: Column(
                  children: [
                    GestureDetector(
                      onTap: _pickImage,
                      child: Container(
                        width: 100,
                        height: 100,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(50),
                          color: accentColor.withValues(alpha: 0.2),
                          border: Border.all(
                            color: accentColor.withValues(alpha: 0.3),
                            width: 2,
                          ),
                        ),
                        child:
                            _selectedImage != null ||
                                    _selectedImageBytes != null
                                ? ClipRRect(
                                  borderRadius: BorderRadius.circular(48),
                                  child:
                                      kIsWeb && _selectedImageBytes != null
                                          ? Image.memory(
                                            _selectedImageBytes!,
                                            width: 96,
                                            height: 96,
                                            fit: BoxFit.cover,
                                          )
                                          : _selectedImage != null
                                          ? Image.file(
                                            _selectedImage!,
                                            width: 96,
                                            height: 96,
                                            fit: BoxFit.cover,
                                          )
                                          : const SizedBox(),
                                )
                                : Icon(
                                  Icons.add_a_photo,
                                  color: accentColor,
                                  size: 32,
                                ),
                      ),
                    ),
                    const SizedBox(height: 16),
                    TextButton(
                      onPressed: _pickImage,
                      child: Text(
                        'Add Team Logo',
                        style: TextStyle(
                          fontFamily: 'Gilroy_Medium',
                          color: accentColor,
                          fontSize: 16,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 24),

              // Team Name Field
              _buildTextField(
                controller: _nameController,
                label: 'Team Name',
                icon: Icons.group,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter team name';
                  }
                  if (value.length < 3) {
                    return 'Team name must be at least 3 characters';
                  }
                  return null;
                },
              ),

              const SizedBox(height: 16),

              // Team Slogan Field
              _buildTextField(
                controller: _sloganController,
                label: 'Team Slogan (Optional)',
                icon: Icons.format_quote,
                validator: (value) {
                  if (value != null && value.isNotEmpty && value.length < 3) {
                    return 'Slogan must be at least 3 characters';
                  }
                  return null;
                },
              ),

              const SizedBox(height: 16),

              // Description Field
              _buildTextField(
                controller: _descriptionController,
                label: 'Description',
                icon: Icons.description,
                maxLines: 4,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter team description';
                  }
                  if (value.length < 10) {
                    return 'Description must be at least 10 characters';
                  }
                  return null;
                },
              ),

              const SizedBox(height: 16),
              // Tips Section
              _buildTipsSection(),

              const SizedBox(height: 32),

              // Create Team Button
              Container(
                width: double.infinity,
                height: 56,
                decoration: NextSportzTheme.getButtonDecoration(),
                child: ElevatedButton(
                  onPressed: _isLoading ? null : _createTeam,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.transparent,
                    shadowColor: Colors.transparent,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20),
                    ),
                  ),
                  child:
                      _isLoading
                          ? const SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                Colors.white,
                              ),
                            ),
                          )
                          : const Text(
                            'Create Team',
                            style: TextStyle(
                              fontFamily: 'Gilroy_Bold',
                              color: Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                ),
              ),

              const SizedBox(height: 24),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    int maxLines = 1,
    String? Function(String?)? validator,
  }) {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final secondaryColor = NextSportzTheme.getSecondaryColor(isDark);
    final accentColor = NextSportzTheme.getAccentColor(isDark);
    final greyColor = NextSportzTheme.getGreyColor(isDark);

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: secondaryColor.withValues(alpha: 0.5),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: TextFormField(
        controller: controller,
        maxLines: maxLines,
        validator: validator,
        style: const TextStyle(
          fontFamily: 'Gilroy_Medium',
          color: Colors.white,
          fontSize: 15,
        ),
        decoration: InputDecoration(
          labelText: label,
          labelStyle: TextStyle(
            fontFamily: 'Gilroy_Medium',
            color: greyColor,
            fontSize: 13,
          ),
          prefixIcon: Icon(icon, color: accentColor, size: 18),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 14,
          ),
        ),
      ),
    );
  }

  Widget _buildTipsSection() {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final accentColor = NextSportzTheme.getAccentColor(isDark);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        color: accentColor.withValues(alpha: 0.1),
        border: Border.all(color: accentColor.withValues(alpha: 0.3), width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.lightbulb_outline, color: accentColor, size: 20),
              const SizedBox(width: 8),
              const Text(
                'Tips for a Great Team',
                style: TextStyle(
                  fontFamily: 'Gilroy_Bold',
                  color: Colors.white,
                  fontSize: 16,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          _buildTipItem('Choose a memorable team name'),
          _buildTipItem('Add a clear description of your team\'s goals'),
          _buildTipItem('Create an inspiring team slogan'),
          _buildTipItem('Upload a team logo to stand out'),
        ],
      ),
    );
  }

  Widget _buildTipItem(String text) {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final accentColor = NextSportzTheme.getAccentColor(isDark);

    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 6,
            height: 6,
            margin: const EdgeInsets.only(top: 8, right: 12),
            decoration: BoxDecoration(
              color: accentColor,
              shape: BoxShape.circle,
            ),
          ),
          Expanded(
            child: Text(
              text,
              style: TextStyle(
                fontFamily: 'Gilroy_Medium',
                color: Colors.white.withValues(alpha: 0.8),
                fontSize: 14,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _pickImage() async {
    await _showImageSourceSelection();
  }

  Future<void> _showImageSourceSelection() async {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final secondaryColor = NextSportzTheme.getSecondaryColor(isDark);
    final accentColor = NextSportzTheme.getAccentColor(isDark);

    await showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder:
          (context) => Container(
            decoration: BoxDecoration(
              color: secondaryColor,
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(20),
              ),
            ),
            padding: const EdgeInsets.all(20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Handle bar
                Container(
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const SizedBox(height: 20),

                // Title
                const Text(
                  'Select Image Source',
                  style: TextStyle(
                    fontFamily: 'Gilroy_Bold',
                    color: Colors.white,
                    fontSize: 18,
                  ),
                ),
                const SizedBox(height: 24),

                // Camera option
                _buildImageSourceOption(
                  icon: Icons.camera_alt,
                  title: 'Camera',
                  subtitle: 'Take a new photo',
                  onTap: () {
                    Navigator.pop(context);
                    _pickImageFromSource(ImageSource.camera);
                  },
                  accentColor: accentColor,
                ),

                const SizedBox(height: 16),

                // Gallery option
                _buildImageSourceOption(
                  icon: Icons.photo_library,
                  title: 'Gallery',
                  subtitle: 'Choose from gallery',
                  onTap: () {
                    Navigator.pop(context);
                    _pickImageFromSource(ImageSource.gallery);
                  },
                  accentColor: accentColor,
                ),

                const SizedBox(height: 20),
              ],
            ),
          ),
    );
  }

  Widget _buildImageSourceOption({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    required Color accentColor,
  }) {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final lightGreyColor = NextSportzTheme.getLightGreyColor(isDark);

    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: lightGreyColor.withValues(alpha: 0.3),
          border: Border.all(
            color: Colors.white.withValues(alpha: 0.1),
            width: 1,
          ),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: accentColor.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Icon(icon, color: accentColor, size: 24),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontFamily: 'Gilroy_Bold',
                      color: Colors.white,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontFamily: 'Gilroy_Medium',
                      color: Colors.white.withValues(alpha: 0.7),
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: Colors.white.withValues(alpha: 0.5),
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _pickImageFromSource(ImageSource source) async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: source,
        maxWidth: 512,
        maxHeight: 512,
        imageQuality: 80,
      );

      if (image != null) {
        if (kIsWeb) {
          // For web, read as bytes
          final bytes = await image.readAsBytes();
          setState(() {
            _selectedImageBytes = bytes;
            _selectedImage = null;
          });
        } else {
          // For mobile, use File
          setState(() {
            _selectedImage = File(image.path);
            _selectedImageBytes = null;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error picking image: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _createTeam() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // Step 1: Create team first without logo
      final createTeamUseCase = ref.read(createTeamUseCaseProvider);
      final createResult = await createTeamUseCase(
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim(),
        slogan:
            _sloganController.text.trim().isEmpty
                ? null
                : _sloganController.text.trim(),
        // Don't include logo in initial creation
      );

      final team = createResult.fold(
        (error) => throw Exception(error.message),
        (team) => team,
      );

      // Step 2: Upload logo if selected and update team
      if (_selectedImage != null || _selectedImageBytes != null) {
        try {
          final imageUploadHelper = ref.read(imageUploadHelperProvider);

          final uploadResult = await imageUploadHelper.uploadTeamLogo(
            file: _selectedImage,
            bytes: _selectedImageBytes,
            teamId: team.id, // Use the actual team ID
            fileName: _selectedImage?.path.split('/').last ?? 'team_logo.jpg',
          );

          await uploadResult.fold(
            (error) async {
              // Show warning but don't fail the team creation
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Warning: Could not upload logo: $error'),
                    backgroundColor: Colors.orange,
                  ),
                );
              }
            },
            (logoUrl) async {
              // Step 3: Update team with logo URL
              final updateTeamUseCase = ref.read(updateTeamUseCaseProvider);
              final updateResult = await updateTeamUseCase(
                teamId: team.id,
                logo: logoUrl,
                description: _descriptionController.text.trim(),
                slogan:
                    _sloganController.text.trim().isEmpty
                        ? null
                        : _sloganController.text.trim(),
                name: _nameController.text.trim(),
              );

              updateResult.fold(
                (error) {
                  // Show warning but don't fail the team creation
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(
                          'Warning: Could not update team logo: ${error.message}',
                        ),
                        backgroundColor: Colors.orange,
                      ),
                    );
                  }
                },
                (updatedTeam) {
                  // Logo updated successfully
                },
              );
            },
          );
        } catch (e) {
          // Show warning but don't fail the team creation
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Warning: Could not upload logo: $e'),
                backgroundColor: Colors.orange,
              ),
            );
          }
        }
      }

      // Refresh teams list
      ref.invalidate(myTeamsProvider);

      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Team created successfully!'),
            backgroundColor: NextSportzTheme.lightAccent,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error creating team: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
