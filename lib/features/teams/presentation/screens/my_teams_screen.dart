import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:nextsportz_v2/core/widgets/expandable_fab.dart';
import '../../../../core/config/theme.dart';
import '../../../../core/providers/theme_provider.dart' as theme_providers;
import '../../teams_providers.dart';
import '../../domain/entities/team.dart';
import '../../domain/usecases/team_invitations_usecases.dart';
import 'create_team_screen.dart';
import 'team_details_screen.dart';
import 'join_team_screen.dart';

class MyTeamsScreen extends ConsumerStatefulWidget {
  const MyTeamsScreen({Key? key}) : super(key: key);

  @override
  ConsumerState<MyTeamsScreen> createState() => _MyTeamsScreenState();
}

class _MyTeamsScreenState extends ConsumerState<MyTeamsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    // Add listener to update FAB visibility when switching tabs
    _tabController.addListener(() {
      setState(() {});
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final primaryColor = NextSportzTheme.getPrimaryColor(isDark);
    final accentColor = NextSportzTheme.getAccentColor(isDark);
    final greyColor = NextSportzTheme.getGreyColor(isDark);
    final teamsAsync = ref.watch(myTeamsProvider);

    return Scaffold(
      backgroundColor: primaryColor,
      appBar: AppBar(
        backgroundColor: primaryColor,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => context.go('/home'),
        ),
        title: const Text(
          'My Teams',
          style: TextStyle(
            fontFamily: 'Gilroy_Bold',
            color: Colors.white,
            fontSize: 18,
          ),
        ),
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: accentColor,
          labelColor: Colors.white,
          unselectedLabelColor: greyColor,
          labelStyle: const TextStyle(fontFamily: 'Gilroy_Bold', fontSize: 16),
          unselectedLabelStyle: const TextStyle(
            fontFamily: 'Gilroy_Medium',
            fontSize: 16,
          ),
          tabs: const [Tab(text: 'My Teams'), Tab(text: 'Invitations')],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [_buildMyTeamsTab(), _buildInvitationsTab()],
      ),
      // Show expandable FAB when on the "My Teams" tab
      floatingActionButton:
          _tabController.index == 0
              ? ExpandableFab(
                distance: 80,
                children: [
                  // Join Team FAB
                  FloatingActionButton.extended(
                    onPressed: () {
                      context.go('/join-team');
                    },
                    backgroundColor: accentColor.withValues(alpha: 0.9),
                    foregroundColor: Colors.white,
                    heroTag: "join_team",
                    icon: const Icon(Icons.group_add, size: 20),
                    label: const Text(
                      'Join Team',
                      style: TextStyle(
                        fontFamily: 'Gilroy_Bold',
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                    ),
                  ),
                  // Create Team FAB
                  FloatingActionButton.extended(
                    onPressed: () {
                      // Keep as modal navigation for create team since it's a form
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const CreateTeamScreen(),
                        ),
                      );
                    },
                    backgroundColor: accentColor,
                    foregroundColor: Colors.white,
                    heroTag: "create_team",
                    icon: const Icon(Icons.add_circle, size: 20),
                    label: const Text(
                      'Create Team',
                      style: TextStyle(
                        fontFamily: 'Gilroy_Bold',
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                    ),
                  ),
                ],
              )
              : null,
    );
  }

  Widget _buildMyTeamsTab() {
    final teamsAsync = ref.watch(myTeamsProvider);

    return teamsAsync.when(
      data:
          (teams) =>
              teams.isEmpty
                  ? _buildEmptyState()
                  : ListView.builder(
                    padding: const EdgeInsets.all(16),
                    itemCount: teams.length,
                    itemBuilder: (context, index) {
                      final team = teams[index];
                      return _buildTeamCard(team);
                    },
                  ),
      loading:
          () => const Center(
            child: CircularProgressIndicator(color: Color(0xff00D4AA)),
          ),
      error: (error, stack) {
        print(stack);
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error_outline, color: Colors.red, size: 64),
              const SizedBox(height: 16),
              Text(
                'Error loading teams',
                style: TextStyle(
                  fontFamily: 'Gilroy_Medium',
                  color: Colors.white,
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 8),
              TextButton(
                onPressed: () => ref.refresh(myTeamsProvider),
                child: const Text(
                  'Retry',
                  style: TextStyle(
                    fontFamily: 'Gilroy_Medium',
                    color: Color(0xff00D4AA),
                    fontSize: 16,
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildInvitationsTab() {
    final invitationsAsync = ref.watch(pendingInvitationsProvider);

    return invitationsAsync.when(
      data: (invitations) {
        // Filter for only active invitations
        final activeInvitations =
            invitations.where((inv) => inv.isActive).toList();

        return activeInvitations.isEmpty
            ? _buildEmptyInvitationsState()
            : ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
              itemCount: activeInvitations.length,
              itemBuilder: (context, index) {
                final invitation = activeInvitations[index];
                return _buildModernReceivedInvitationCard(invitation, index);
              },
            );
      },
      loading:
          () => const Center(
            child: CircularProgressIndicator(color: Color(0xff00D4AA)),
          ),
      error:
          (error, stack) => Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.error_outline, color: Colors.red, size: 64),
                const SizedBox(height: 16),
                Text(
                  'Error loading invitations',
                  style: TextStyle(
                    fontFamily: 'Gilroy_Medium',
                    color: Colors.white,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 8),
                TextButton(
                  onPressed: () => ref.refresh(pendingInvitationsProvider),
                  child: const Text(
                    'Retry',
                    style: TextStyle(
                      fontFamily: 'Gilroy_Medium',
                      color: Color(0xff00D4AA),
                      fontSize: 16,
                    ),
                  ),
                ),
              ],
            ),
          ),
    );
  }

  Widget _buildTeamCard(TeamSearchItem team) {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final secondaryColor = NextSportzTheme.getSecondaryColor(isDark);
    final greyColor = NextSportzTheme.getGreyColor(isDark);
    final invitations = [];
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        color: secondaryColor.withOpacity(0.5),
        border: Border.all(color: Colors.white.withOpacity(0.1), width: 1),
      ),
      child: InkWell(
        onTap: () {
          context.go('/my-teams/${team.id}');
        },
        borderRadius: BorderRadius.circular(20),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(15),
                      color: const Color(0xff00D4AA).withOpacity(0.2),
                    ),
                    child:
                        team.logoUrl.isNotEmpty
                            ? ClipRRect(
                              borderRadius: BorderRadius.circular(15),
                              child: CachedNetworkImage(
                                imageUrl: team.logoUrl,
                                fit: BoxFit.cover,
                                width: 60,
                                height: 60,
                                placeholder:
                                    (context, url) => Container(
                                      width: 60,
                                      height: 60,
                                      decoration: BoxDecoration(
                                        color: Colors.white.withOpacity(0.1),
                                        borderRadius: BorderRadius.circular(15),
                                      ),
                                      child: const Center(
                                        child: CircularProgressIndicator(
                                          color: Color(0xff00D4AA),
                                          strokeWidth: 2,
                                        ),
                                      ),
                                    ),
                                errorWidget:
                                    (context, url, error) => Container(
                                      width: 60,
                                      height: 60,
                                      decoration: BoxDecoration(
                                        color: Colors.white.withOpacity(0.1),
                                        borderRadius: BorderRadius.circular(15),
                                      ),
                                      child: const Icon(
                                        Icons.sports_soccer,
                                        color: Colors.white,
                                        size: 30,
                                      ),
                                    ),
                              ),
                            )
                            : const Icon(
                              Icons.sports_soccer,
                              color: Color(0xff00D4AA),
                              size: 30,
                            ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          team.name,
                          style: const TextStyle(
                            fontFamily: 'Gilroy_Bold',
                            color: Colors.white,
                            fontSize: 18,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'Football Team',
                          style: TextStyle(
                            fontFamily: 'Gilroy_Medium',
                            color: greyColor,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Icon(Icons.arrow_forward_ios, color: greyColor, size: 16),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                'Football Team',
                style: TextStyle(
                  fontFamily: 'Gilroy_Medium',
                  color: Colors.white.withOpacity(0.8),
                  fontSize: 14,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 10),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildStatItem(
                    'Members',
                    '${team.membersCount}',
                    Icons.people,
                  ),
                  _buildStatItem('Matches', '-', Icons.sports_soccer),
                  _buildStatItem(
                    'Win Rate',
                    team.winRatePercentage,
                    Icons.trending_up,
                  ),
                ],
              ),
              if (invitations.isNotEmpty) ...[
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    color: Colors.orange.withOpacity(0.2),
                  ),
                  child: Text(
                    '${invitations} pending invitation${invitations.length > 1 ? 's' : ''}',
                    style: const TextStyle(
                      fontFamily: 'Gilroy_Medium',
                      color: Colors.orange,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildModernReceivedInvitationCard(
    TeamInvitation invitation,
    int index,
  ) {
    return TweenAnimationBuilder<double>(
      duration: Duration(milliseconds: 300 + (index * 100)),
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return Transform.translate(
          offset: Offset(0, 20 * (1 - value)),
          child: Opacity(
            opacity: value,
            child: Container(
              margin: const EdgeInsets.only(bottom: 12),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Colors.white.withOpacity(0.08),
                    Colors.white.withOpacity(0.04),
                  ],
                ),
                border: Border.all(
                  color: Colors.white.withOpacity(0.12),
                  width: 0.5,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        CircleAvatar(
                          radius: 25,
                          backgroundColor: const Color(
                            0xff00D4AA,
                          ).withOpacity(0.2),
                          child: Text(
                            invitation.invitedTeamName[0].toUpperCase(),
                            style: const TextStyle(
                              fontFamily: 'Gilroy_Bold',
                              color: Color(0xff00D4AA),
                              fontSize: 18,
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                invitation.invitedTeamName,
                                style: const TextStyle(
                                  fontFamily: 'Gilroy_Bold',
                                  color: Colors.white,
                                  fontSize: 16,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                'Invited ${invitation.timeAgo}',
                                style: TextStyle(
                                  fontFamily: 'Gilroy_Medium',
                                  color: Colors.white.withOpacity(0.6),
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),
                        ),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: const Color(0xffFFA726).withOpacity(0.15),
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: const Color(0xffFFA726).withOpacity(0.3),
                              width: 0.5,
                            ),
                          ),
                          child: Text(
                            'PENDING',
                            style: TextStyle(
                              fontFamily: 'Gilroy_Bold',
                              color: const Color(0xffFFA726),
                              fontSize: 10,
                              fontWeight: FontWeight.w600,
                              letterSpacing: 0.5,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: _buildActionButton(
                            label: 'Accept',
                            onTap: () => _acceptInvitation(invitation.id),
                            isPrimary: true,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: _buildActionButton(
                            label: 'Decline',
                            onTap: () => _declineInvitation(invitation.id),
                            isPrimary: false,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildActionButton({
    required String label,
    required VoidCallback onTap,
    required bool isPrimary,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(10),
        child: Container(
          height: 36,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
            gradient:
                isPrimary
                    ? LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        const Color(0xff00D4AA),
                        const Color(0xff00D4AA).withOpacity(0.8),
                      ],
                    )
                    : null,
            border:
                isPrimary
                    ? null
                    : Border.all(
                      color: Colors.white.withOpacity(0.2),
                      width: 0.5,
                    ),
            color: isPrimary ? null : Colors.white.withOpacity(0.05),
          ),
          child: Center(
            child: Text(
              label,
              style: TextStyle(
                fontFamily: 'Gilroy_SemiBold',
                color: isPrimary ? Colors.white : Colors.white.withOpacity(0.8),
                fontSize: 13,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(icon, color: const Color(0xff00D4AA), size: 16),
        const SizedBox(width: 3),
        Text(
          '$value',
          style: const TextStyle(
            fontFamily: 'Gilroy_Bold',
            color: Colors.white,
            fontSize: 14,
          ),
        ),
        const SizedBox(width: 3),
        Text(
          label,
          style: TextStyle(
            fontFamily: 'Gilroy_Medium',
            color: NextSportzTheme.getGreyColor(
              ref.watch(theme_providers.isDarkModeProvider),
            ),
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  Widget _buildEmptyState() {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final accentColor = NextSportzTheme.getAccentColor(isDark);
    final greyColor = NextSportzTheme.getGreyColor(isDark);

    return Container(
      padding: const EdgeInsets.all(24),
      child: SingleChildScrollView(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Animated illustration container
            Container(
              width: 160,
              height: 160,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(80),
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    const Color(0xff00D4AA).withOpacity(0.3),
                    const Color(0xff00D4AA).withOpacity(0.1),
                  ],
                ),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xff00D4AA).withOpacity(0.3),
                    blurRadius: 20,
                    spreadRadius: 5,
                  ),
                ],
              ),
              child: Stack(
                alignment: Alignment.center,
                children: [
                  // Background circles for visual appeal
                  Positioned(
                    top: 15,
                    left: 15,
                    child: Container(
                      width: 30,
                      height: 30,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: Colors.white.withOpacity(0.1),
                      ),
                    ),
                  ),
                  Positioned(
                    bottom: 25,
                    right: 20,
                    child: Container(
                      width: 25,
                      height: 25,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: Colors.white.withOpacity(0.1),
                      ),
                    ),
                  ),
                  // Main icon
                  const Icon(
                    Icons.sports_soccer,
                    color: Color(0xff00D4AA),
                    size: 64,
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // Main heading with emoji
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Text('🏆 ', style: TextStyle(fontSize: 24)),
                const Text(
                  'Ready to Lead?',
                  style: TextStyle(
                    fontFamily: 'Gilroy_Bold',
                    color: Colors.white,
                    fontSize: 24,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            // Engaging subtitle
            Text(
              'Create your dream team and start your journey to victory!',
              style: TextStyle(
                fontFamily: 'Gilroy_Medium',
                color: greyColor,
                fontSize: 15,
                height: 1.4,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 20),

            // Benefits list - more compact
            Container(
              margin: const EdgeInsets.only(bottom: 20),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),
                color: Colors.white.withOpacity(0.05),
                border: Border.all(
                  color: const Color(0xff00D4AA).withOpacity(0.2),
                  width: 1,
                ),
              ),
              child: Column(
                children: [
                  _buildBenefitItem('👥', 'Build your perfect squad'),
                  _buildBenefitItem('🏅', 'Compete in tournaments'),
                  _buildBenefitItem('📊', 'Track team performance'),
                  _buildBenefitItem('🎯', 'Achieve greatness together'),
                ],
              ),
            ),

            // Action buttons
            Column(
              children: [
                // Create Team button
                SizedBox(
                  width: double.infinity,
                  height: 60,
                  child: OutlinedButton(
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const CreateTeamScreen(),
                        ),
                      );
                    },
                    style: OutlinedButton.styleFrom(
                      side: const BorderSide(
                        color: Color(0xff00D4AA),
                        width: 2,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20),
                      ),
                      backgroundColor: Colors.transparent,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 24,
                        vertical: 16,
                      ),
                    ),
                    child: const Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.add_circle_outline,
                          color: Color(0xff00D4AA),
                          size: 28,
                        ),
                        SizedBox(width: 12),
                        Text(
                          'Create Your First Team',
                          style: TextStyle(
                            fontFamily: 'Gilroy_Bold',
                            color: Color(0xff00D4AA),
                            fontSize: 18,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: 16),

                // Join Team button
                SizedBox(
                  width: double.infinity,
                  height: 60,
                  child: ElevatedButton(
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const JoinTeamScreen(),
                        ),
                      );
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: accentColor,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20),
                      ),
                      padding: const EdgeInsets.symmetric(
                        horizontal: 24,
                        vertical: 16,
                      ),
                    ),
                    child: const Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.group_add, color: Colors.white, size: 28),
                        SizedBox(width: 12),
                        Text(
                          'Join Existing Team',
                          style: TextStyle(
                            fontFamily: 'Gilroy_Bold',
                            color: Colors.white,
                            fontSize: 18,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBenefitItem(String emoji, String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6),
      child: Row(
        children: [
          Text(emoji, style: const TextStyle(fontSize: 20)),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              text,
              style: const TextStyle(
                fontFamily: 'Gilroy_Medium',
                color: Colors.white,
                fontSize: 14,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyInvitationsState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(60),
              color: const Color(0xff00D4AA).withOpacity(0.2),
            ),
            child: const Icon(
              Icons.mail_outline,
              color: Color(0xff00D4AA),
              size: 60,
            ),
          ),
          const SizedBox(height: 24),
          const Text(
            'No Pending Invitations',
            style: TextStyle(
              fontFamily: 'Gilroy_Bold',
              color: Colors.white,
              fontSize: 24,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'You don\'t have any pending team invitations',
            style: TextStyle(
              fontFamily: 'Gilroy_Medium',
              color: NextSportzTheme.getGreyColor(
                ref.watch(theme_providers.isDarkModeProvider),
              ),
              fontSize: 16,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'today';
    } else if (difference.inDays == 1) {
      return 'yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }

  Future<void> _acceptInvitation(String invitationId) async {
    try {
      final useCase = ref.read(acceptInvitationUseCaseProvider);
      // Note: We need teamId for the new API, but it's not available here.
      // This might need to be refactored to pass the full invitation object.
      // For now, using a placeholder teamId.
      // Get the invitation to extract teamId
      final invitations = await ref.read(pendingInvitationsProvider.future);
      final invitation = invitations.firstWhere(
        (inv) => inv.id == invitationId,
      );

      final params = AcceptInvitationParams(
        teamId: invitation.invitedTeamId,
        invitationId: invitationId,
      );
      await useCase(params);

      // Refresh the providers to update the UI
      ref.invalidate(pendingInvitationsProvider);
      ref.invalidate(myTeamsProvider);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Invitation accepted successfully!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error accepting invitation: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _declineInvitation(String invitationId) async {
    try {
      final useCase = ref.read(declineInvitationUseCaseProvider);
      // Note: We need teamId for the new API, but it's not available here.
      // This might need to be refactored to pass the full invitation object.
      // For now, using a placeholder teamId.
      // Get the invitation to extract teamId
      final invitations = await ref.read(pendingInvitationsProvider.future);
      final invitation = invitations.firstWhere(
        (inv) => inv.id == invitationId,
      );

      final params = DeclineInvitationParams(
        teamId: invitation.invitedTeamId,
        invitationId: invitationId,
      );
      await useCase(params);

      // Refresh the provider to update the UI
      ref.invalidate(pendingInvitationsProvider);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Invitation declined'),
            backgroundColor: Colors.orange,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error declining invitation: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
